import { createSlice, PayloadAction, createAsyncThunk } from '@reduxjs/toolkit';
import { VisualEffect, FilterPreset } from '@/components/VisualEffectsProcessor';
import { ImageCaching } from '@/lib/utils/imageCaching';

interface EditingHistory {
  id: string;
  originalUri: string;
  editedUri: string;
  operations: EditOperation[];
  timestamp: number;
}

interface EditOperation {
  type: 'filter' | 'crop' | 'rotate' | 'flip' | 'text' | 'adjust';
  data: any;
  timestamp: number;
}

interface TextOverlay {
  id: string;
  text: string;
  x: number;
  y: number;
  fontSize: number;
  color: string;
  fontFamily: string;
  rotation: number;
}

interface ImageEditingState {
  // Current editing session
  currentImageUri: string | null;
  originalImageUri: string | null;
  editingHistory: EditingHistory[];
  currentHistoryIndex: number;
  
  // Applied effects and modifications
  appliedFilter: FilterPreset | null;
  appliedEffects: VisualEffect;
  textOverlays: TextOverlay[];
  cropData: {
    x: number;
    y: number;
    width: number;
    height: number;
  } | null;
  transformations: {
    rotation: number;
    flipHorizontal: boolean;
    flipVertical: boolean;
  };
  
  // UI state
  editingMode: 'basic' | 'advanced';
  activeEditingTool: 'filters' | 'adjust' | 'crop' | 'text' | 'transform' | null;
  isProcessing: boolean;
  processingProgress: number;
  
  // Performance and caching
  previewCache: Record<string, string>;
  lastCacheCleanup: number;
  
  // Error handling
  error: string | null;
  lastOperation: EditOperation | null;
}

const initialState: ImageEditingState = {
  // Current editing session
  currentImageUri: null,
  originalImageUri: null,
  editingHistory: [],
  currentHistoryIndex: -1,
  
  // Applied effects and modifications
  appliedFilter: null,
  appliedEffects: {},
  textOverlays: [],
  cropData: null,
  transformations: {
    rotation: 0,
    flipHorizontal: false,
    flipVertical: false,
  },
  
  // UI state
  editingMode: 'basic',
  activeEditingTool: null,
  isProcessing: false,
  processingProgress: 0,
  
  // Performance and caching
  previewCache: {},
  lastCacheCleanup: Date.now(),
  
  // Error handling
  error: null,
  lastOperation: null,
};

// Async thunks for image processing
export const processImageWithEffects = createAsyncThunk(
  'imageEditing/processImageWithEffects',
  async (
    { imageUri, effects, options }: {
      imageUri: string;
      effects: VisualEffect;
      options?: { maintainDimensions?: boolean; quality?: number };
    },
    { dispatch, rejectWithValue }
  ) => {
    try {
      dispatch(setProcessing(true));
      dispatch(setProcessingProgress(0));
      
      // Import the processor dynamically to avoid circular dependencies
      const { VisualEffectsProcessor } = await import('@/components/VisualEffectsProcessor');
      const processor = VisualEffectsProcessor.getInstance();
      
      dispatch(setProcessingProgress(50));
      
      const processedUri = await processor.applyVisualEffect(imageUri, effects, options);
      
      dispatch(setProcessingProgress(100));
      
      // Cache the processed image for performance
      await ImageCaching.cacheImageFromUrl(processedUri);
      
      return processedUri;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to process image');
    } finally {
      dispatch(setProcessing(false));
      dispatch(setProcessingProgress(0));
    }
  }
);

export const generateFilterPreviews = createAsyncThunk(
  'imageEditing/generateFilterPreviews',
  async (
    { imageUri, filters }: { imageUri: string; filters: FilterPreset[] },
    { dispatch, getState, rejectWithValue }
  ) => {
    try {
      dispatch(setProcessing(true));
      
      const { VisualEffectsProcessor } = await import('@/components/VisualEffectsProcessor');
      const processor = VisualEffectsProcessor.getInstance();
      
      const previews = await processor.generateBatchPreviews(
        imageUri,
        filters,
        120,
        (completed, total) => {
          dispatch(setProcessingProgress((completed / total) * 100));
        }
      );
      
      return previews;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to generate previews');
    } finally {
      dispatch(setProcessing(false));
      dispatch(setProcessingProgress(0));
    }
  }
);

const imageEditingSlice = createSlice({
  name: 'imageEditing',
  initialState,
  reducers: {
    // Session management
    startEditingSession: (state, action: PayloadAction<string>) => {
      state.currentImageUri = action.payload;
      state.originalImageUri = action.payload;
      state.editingHistory = [];
      state.currentHistoryIndex = -1;
      state.appliedFilter = null;
      state.appliedEffects = {};
      state.textOverlays = [];
      state.cropData = null;
      state.transformations = {
        rotation: 0,
        flipHorizontal: false,
        flipVertical: false,
      };
      state.error = null;
    },
    
    endEditingSession: (state) => {
      state.currentImageUri = null;
      state.originalImageUri = null;
      state.editingHistory = [];
      state.currentHistoryIndex = -1;
      state.activeEditingTool = null;
      state.error = null;
    },
    
    // History management
    addToHistory: (state, action: PayloadAction<{ uri: string; operation: EditOperation }>) => {
      const { uri, operation } = action.payload;
      
      // Remove any history after current index (for redo functionality)
      state.editingHistory = state.editingHistory.slice(0, state.currentHistoryIndex + 1);
      
      // Add new history entry
      const historyEntry: EditingHistory = {
        id: Date.now().toString(),
        originalUri: state.originalImageUri!,
        editedUri: uri,
        operations: [...(state.editingHistory[state.currentHistoryIndex]?.operations || []), operation],
        timestamp: Date.now(),
      };
      
      state.editingHistory.push(historyEntry);
      state.currentHistoryIndex = state.editingHistory.length - 1;
      state.currentImageUri = uri;
      state.lastOperation = operation;
    },
    
    undo: (state) => {
      if (state.currentHistoryIndex > 0) {
        state.currentHistoryIndex--;
        const previousEntry = state.editingHistory[state.currentHistoryIndex];
        state.currentImageUri = previousEntry.editedUri;
      } else if (state.currentHistoryIndex === 0) {
        state.currentHistoryIndex = -1;
        state.currentImageUri = state.originalImageUri;
      }
    },
    
    redo: (state) => {
      if (state.currentHistoryIndex < state.editingHistory.length - 1) {
        state.currentHistoryIndex++;
        const nextEntry = state.editingHistory[state.currentHistoryIndex];
        state.currentImageUri = nextEntry.editedUri;
      }
    },
    
    // Effects and modifications
    setAppliedFilter: (state, action: PayloadAction<FilterPreset | null>) => {
      state.appliedFilter = action.payload;
    },
    
    setAppliedEffects: (state, action: PayloadAction<VisualEffect>) => {
      state.appliedEffects = action.payload;
    },
    
    addTextOverlay: (state, action: PayloadAction<TextOverlay>) => {
      state.textOverlays.push(action.payload);
    },
    
    updateTextOverlay: (state, action: PayloadAction<{ id: string; updates: Partial<TextOverlay> }>) => {
      const { id, updates } = action.payload;
      const index = state.textOverlays.findIndex(overlay => overlay.id === id);
      if (index !== -1) {
        state.textOverlays[index] = { ...state.textOverlays[index], ...updates };
      }
    },
    
    removeTextOverlay: (state, action: PayloadAction<string>) => {
      state.textOverlays = state.textOverlays.filter(overlay => overlay.id !== action.payload);
    },
    
    setCropData: (state, action: PayloadAction<typeof initialState.cropData>) => {
      state.cropData = action.payload;
    },
    
    setTransformations: (state, action: PayloadAction<typeof initialState.transformations>) => {
      state.transformations = action.payload;
    },
    
    // UI state
    setEditingMode: (state, action: PayloadAction<'basic' | 'advanced'>) => {
      state.editingMode = action.payload;
    },
    
    setActiveEditingTool: (state, action: PayloadAction<typeof initialState.activeEditingTool>) => {
      state.activeEditingTool = action.payload;
    },
    
    setProcessing: (state, action: PayloadAction<boolean>) => {
      state.isProcessing = action.payload;
    },
    
    setProcessingProgress: (state, action: PayloadAction<number>) => {
      state.processingProgress = action.payload;
    },
    
    // Cache management
    updatePreviewCache: (state, action: PayloadAction<Record<string, string>>) => {
      state.previewCache = { ...state.previewCache, ...action.payload };
    },
    
    clearPreviewCache: (state) => {
      state.previewCache = {};
      state.lastCacheCleanup = Date.now();
    },
    
    // Error handling
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(processImageWithEffects.fulfilled, (state, action) => {
        // Image processing completed successfully
        state.error = null;
      })
      .addCase(processImageWithEffects.rejected, (state, action) => {
        state.error = action.payload as string;
      })
      .addCase(generateFilterPreviews.fulfilled, (state, action) => {
        state.previewCache = { ...state.previewCache, ...action.payload };
        state.error = null;
      })
      .addCase(generateFilterPreviews.rejected, (state, action) => {
        state.error = action.payload as string;
      });
  },
});

export const {
  // Session management
  startEditingSession,
  endEditingSession,
  
  // History management
  addToHistory,
  undo,
  redo,
  
  // Effects and modifications
  setAppliedFilter,
  setAppliedEffects,
  addTextOverlay,
  updateTextOverlay,
  removeTextOverlay,
  setCropData,
  setTransformations,
  
  // UI state
  setEditingMode,
  setActiveEditingTool,
  setProcessing,
  setProcessingProgress,
  
  // Cache management
  updatePreviewCache,
  clearPreviewCache,
  
  // Error handling
  setError,
  clearError,
} = imageEditingSlice.actions;

export default imageEditingSlice.reducer;
