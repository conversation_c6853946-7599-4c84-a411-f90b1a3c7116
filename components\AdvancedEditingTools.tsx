import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  PanGestureHandler,
  PinchGestureHandler,
  State,
  TextInput,
  ScrollView,
  Alert,
} from 'react-native';
import Animated, {
  useAnimatedGestureHandler,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  runOnJS,
} from 'react-native-reanimated';
import { Feather } from '@expo/vector-icons';
import { useTheme } from '@/src/context/ThemeContext';
import * as ImageManipulator from 'expo-image-manipulator';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

interface AdvancedEditingToolsProps {
  imageUri: string;
  onImageEdited: (editedUri: string) => void;
  onClose: () => void;
}

interface CropArea {
  x: number;
  y: number;
  width: number;
  height: number;
}

interface TextOverlay {
  id: string;
  text: string;
  x: number;
  y: number;
  fontSize: number;
  color: string;
  fontFamily: string;
  rotation: number;
}

const AdvancedEditingTools: React.FC<AdvancedEditingToolsProps> = ({
  imageUri,
  onImageEdited,
  onClose,
}) => {
  const { colors, isDarkMode } = useTheme();
  
  // Tool selection
  const [currentTool, setCurrentTool] = useState<'crop' | 'text' | 'adjust' | 'rotate'>('crop');
  
  // Crop tool state
  const [cropMode, setCropMode] = useState(false);
  const [cropAspectRatio, setCropAspectRatio] = useState<'free' | '1:1' | '4:3' | '16:9' | '9:16'>('free');
  const [cropArea, setCropArea] = useState<CropArea>({ x: 0, y: 0, width: 100, height: 100 });
  
  // Text overlay state
  const [textOverlays, setTextOverlays] = useState<TextOverlay[]>([]);
  const [selectedTextId, setSelectedTextId] = useState<string | null>(null);
  const [newText, setNewText] = useState('');
  const [textColor, setTextColor] = useState('#FFFFFF');
  const [fontSize, setFontSize] = useState(24);
  const [fontFamily, setFontFamily] = useState('Rubik');
  
  // Adjustment state
  const [rotation, setRotation] = useState(0);
  const [flipHorizontal, setFlipHorizontal] = useState(false);
  const [flipVertical, setFlipVertical] = useState(false);
  
  // Animation values for crop handles
  const cropX = useSharedValue(50);
  const cropY = useSharedValue(50);
  const cropWidth = useSharedValue(200);
  const cropHeight = useSharedValue(200);

  // Font options
  const fontOptions = [
    { name: 'Rubik', label: 'Rubik' },
    { name: 'System', label: 'System' },
    { name: 'serif', label: 'Serif' },
    { name: 'monospace', label: 'Mono' },
  ];

  // Color options for text
  const colorOptions = [
    '#FFFFFF', '#000000', '#FF0000', '#00FF00', '#0000FF',
    '#FFFF00', '#FF00FF', '#00FFFF', '#FFA500', '#800080',
  ];

  // Aspect ratio options
  const aspectRatios = [
    { label: 'Free', value: 'free' as const },
    { label: '1:1', value: '1:1' as const },
    { label: '4:3', value: '4:3' as const },
    { label: '16:9', value: '16:9' as const },
    { label: '9:16', value: '9:16' as const },
  ];

  // Apply crop to image
  const applyCrop = async () => {
    try {
      const cropData = {
        originX: cropArea.x,
        originY: cropArea.y,
        width: cropArea.width,
        height: cropArea.height,
      };

      const result = await ImageManipulator.manipulateAsync(
        imageUri,
        [{ crop: cropData }],
        {
          compress: 0.9,
          format: ImageManipulator.SaveFormat.JPEG,
        }
      );

      onImageEdited(result.uri);
    } catch (error) {
      console.error('Error applying crop:', error);
      Alert.alert('Error', 'Failed to crop image. Please try again.');
    }
  };

  // Apply rotation and flips
  const applyTransformations = async () => {
    try {
      const actions: ImageManipulator.Action[] = [];

      if (rotation !== 0) {
        actions.push({ rotate: rotation });
      }

      if (flipHorizontal) {
        actions.push({ flip: ImageManipulator.FlipType.Horizontal });
      }

      if (flipVertical) {
        actions.push({ flip: ImageManipulator.FlipType.Vertical });
      }

      if (actions.length === 0) {
        return; // No transformations to apply
      }

      const result = await ImageManipulator.manipulateAsync(
        imageUri,
        actions,
        {
          compress: 0.9,
          format: ImageManipulator.SaveFormat.JPEG,
        }
      );

      onImageEdited(result.uri);
    } catch (error) {
      console.error('Error applying transformations:', error);
      Alert.alert('Error', 'Failed to apply transformations. Please try again.');
    }
  };

  // Add text overlay
  const addTextOverlay = () => {
    if (!newText.trim()) {
      Alert.alert('Error', 'Please enter some text');
      return;
    }

    const overlay: TextOverlay = {
      id: Date.now().toString(),
      text: newText,
      x: SCREEN_WIDTH / 2,
      y: SCREEN_HEIGHT / 2,
      fontSize,
      color: textColor,
      fontFamily,
      rotation: 0,
    };

    setTextOverlays([...textOverlays, overlay]);
    setNewText('');
  };

  // Remove text overlay
  const removeTextOverlay = (id: string) => {
    setTextOverlays(textOverlays.filter(overlay => overlay.id !== id));
    setSelectedTextId(null);
  };

  // Update text overlay
  const updateTextOverlay = (id: string, updates: Partial<TextOverlay>) => {
    setTextOverlays(textOverlays.map(overlay => 
      overlay.id === id ? { ...overlay, ...updates } : overlay
    ));
  };

  // Crop gesture handlers
  const cropPanGestureHandler = useAnimatedGestureHandler({
    onStart: (_, context) => {
      context.startX = cropX.value;
      context.startY = cropY.value;
    },
    onActive: (event, context) => {
      cropX.value = context.startX + event.translationX;
      cropY.value = context.startY + event.translationY;
    },
    onEnd: () => {
      runOnJS(setCropArea)({
        x: cropX.value,
        y: cropY.value,
        width: cropWidth.value,
        height: cropHeight.value,
      });
    },
  });

  const cropAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { translateX: cropX.value },
        { translateY: cropY.value },
      ],
      width: cropWidth.value,
      height: cropHeight.value,
    };
  });

  // Render crop tool
  const renderCropTool = () => (
    <View style={styles.toolContainer}>
      <Text style={[styles.toolTitle, { color: colors.text }]}>Crop Image</Text>
      
      {/* Aspect Ratio Selector */}
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.aspectRatioContainer}>
        {aspectRatios.map((ratio) => (
          <TouchableOpacity
            key={ratio.value}
            style={[
              styles.aspectRatioButton,
              { backgroundColor: colors.backgroundSecondary },
              cropAspectRatio === ratio.value && { backgroundColor: colors.primary },
            ]}
            onPress={() => setCropAspectRatio(ratio.value)}
          >
            <Text
              style={[
                styles.aspectRatioText,
                { color: cropAspectRatio === ratio.value ? '#FFFFFF' : colors.text },
              ]}
            >
              {ratio.label}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* Crop Controls */}
      <View style={styles.cropControls}>
        <TouchableOpacity
          style={[styles.cropButton, { backgroundColor: colors.backgroundSecondary }]}
          onPress={() => setCropMode(!cropMode)}
        >
          <Feather name="crop" size={20} color={colors.text} />
          <Text style={[styles.cropButtonText, { color: colors.text }]}>
            {cropMode ? 'Exit Crop' : 'Start Crop'}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.cropButton, { backgroundColor: colors.primary }]}
          onPress={applyCrop}
          disabled={!cropMode}
        >
          <Feather name="check" size={20} color="#FFFFFF" />
          <Text style={[styles.cropButtonText, { color: '#FFFFFF' }]}>Apply</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  // Render text tool
  const renderTextTool = () => (
    <View style={styles.toolContainer}>
      <Text style={[styles.toolTitle, { color: colors.text }]}>Add Text</Text>
      
      {/* Text Input */}
      <View style={styles.textInputContainer}>
        <TextInput
          style={[styles.textInput, { color: colors.text, borderColor: colors.border }]}
          placeholder="Enter text..."
          placeholderTextColor={colors.textSecondary}
          value={newText}
          onChangeText={setNewText}
          multiline
        />
        <TouchableOpacity
          style={[styles.addButton, { backgroundColor: colors.primary }]}
          onPress={addTextOverlay}
        >
          <Feather name="plus" size={20} color="#FFFFFF" />
        </TouchableOpacity>
      </View>

      {/* Font Size Slider */}
      <View style={styles.sliderContainer}>
        <Text style={[styles.sliderLabel, { color: colors.text }]}>Size: {fontSize}px</Text>
        <View style={styles.sliderRow}>
          <TouchableOpacity onPress={() => setFontSize(Math.max(12, fontSize - 2))}>
            <Feather name="minus" size={20} color={colors.text} />
          </TouchableOpacity>
          <View style={[styles.sliderTrack, { backgroundColor: colors.backgroundSecondary }]}>
            <View 
              style={[
                styles.sliderThumb, 
                { 
                  backgroundColor: colors.primary,
                  left: `${((fontSize - 12) / (48 - 12)) * 100}%`
                }
              ]} 
            />
          </View>
          <TouchableOpacity onPress={() => setFontSize(Math.min(48, fontSize + 2))}>
            <Feather name="plus" size={20} color={colors.text} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Font Family Selector */}
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.fontContainer}>
        {fontOptions.map((font) => (
          <TouchableOpacity
            key={font.name}
            style={[
              styles.fontButton,
              { backgroundColor: colors.backgroundSecondary },
              fontFamily === font.name && { backgroundColor: colors.primary },
            ]}
            onPress={() => setFontFamily(font.name)}
          >
            <Text
              style={[
                styles.fontText,
                { 
                  color: fontFamily === font.name ? '#FFFFFF' : colors.text,
                  fontFamily: font.name,
                },
              ]}
            >
              {font.label}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* Color Selector */}
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.colorContainer}>
        {colorOptions.map((color) => (
          <TouchableOpacity
            key={color}
            style={[
              styles.colorButton,
              { backgroundColor: color },
              textColor === color && styles.selectedColor,
            ]}
            onPress={() => setTextColor(color)}
          />
        ))}
      </ScrollView>
    </View>
  );

  // Render rotate tool
  const renderRotateTool = () => (
    <View style={styles.toolContainer}>
      <Text style={[styles.toolTitle, { color: colors.text }]}>Transform</Text>
      
      {/* Rotation Controls */}
      <View style={styles.rotationControls}>
        <TouchableOpacity
          style={[styles.rotateButton, { backgroundColor: colors.backgroundSecondary }]}
          onPress={() => setRotation((rotation - 90) % 360)}
        >
          <Feather name="rotate-ccw" size={24} color={colors.text} />
          <Text style={[styles.rotateButtonText, { color: colors.text }]}>Rotate Left</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.rotateButton, { backgroundColor: colors.backgroundSecondary }]}
          onPress={() => setRotation((rotation + 90) % 360)}
        >
          <Feather name="rotate-cw" size={24} color={colors.text} />
          <Text style={[styles.rotateButtonText, { color: colors.text }]}>Rotate Right</Text>
        </TouchableOpacity>
      </View>

      {/* Flip Controls */}
      <View style={styles.flipControls}>
        <TouchableOpacity
          style={[
            styles.flipButton,
            { backgroundColor: flipHorizontal ? colors.primary : colors.backgroundSecondary },
          ]}
          onPress={() => setFlipHorizontal(!flipHorizontal)}
        >
          <Feather name="move-horizontal" size={20} color={flipHorizontal ? '#FFFFFF' : colors.text} />
          <Text style={[styles.flipButtonText, { color: flipHorizontal ? '#FFFFFF' : colors.text }]}>
            Flip H
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.flipButton,
            { backgroundColor: flipVertical ? colors.primary : colors.backgroundSecondary },
          ]}
          onPress={() => setFlipVertical(!flipVertical)}
        >
          <Feather name="move-vertical" size={20} color={flipVertical ? '#FFFFFF' : colors.text} />
          <Text style={[styles.flipButtonText, { color: flipVertical ? '#FFFFFF' : colors.text }]}>
            Flip V
          </Text>
        </TouchableOpacity>
      </View>

      {/* Apply Button */}
      <TouchableOpacity
        style={[styles.applyButton, { backgroundColor: colors.primary }]}
        onPress={applyTransformations}
      >
        <Feather name="check" size={20} color="#FFFFFF" />
        <Text style={[styles.applyButtonText, { color: '#FFFFFF' }]}>Apply Changes</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { borderBottomColor: colors.border }]}>
        <TouchableOpacity onPress={onClose}>
          <Feather name="x" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>Edit Tools</Text>
        <View style={{ width: 24 }} />
      </View>

      {/* Tool Tabs */}
      <View style={[styles.tabContainer, { borderBottomColor: colors.border }]}>
        {[
          { key: 'crop', icon: 'crop', label: 'Crop' },
          { key: 'text', icon: 'type', label: 'Text' },
          { key: 'rotate', icon: 'rotate-cw', label: 'Transform' },
        ].map((tool) => (
          <TouchableOpacity
            key={tool.key}
            style={[
              styles.tab,
              currentTool === tool.key && { borderBottomColor: colors.primary },
            ]}
            onPress={() => setCurrentTool(tool.key as any)}
          >
            <Feather 
              name={tool.icon as any} 
              size={20} 
              color={currentTool === tool.key ? colors.primary : colors.textSecondary} 
            />
            <Text
              style={[
                styles.tabText,
                { color: currentTool === tool.key ? colors.primary : colors.textSecondary },
              ]}
            >
              {tool.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Tool Content */}
      <ScrollView style={styles.toolContent} showsVerticalScrollIndicator={false}>
        {currentTool === 'crop' && renderCropTool()}
        {currentTool === 'text' && renderTextTool()}
        {currentTool === 'rotate' && renderRotateTool()}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    fontFamily: 'Rubik',
  },
  tabContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    backgroundColor: 'rgba(128, 128, 128, 0.05)',
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  tabText: {
    fontSize: 14,
    fontWeight: '500',
    fontFamily: 'Rubik',
    marginLeft: 8,
  },
  toolContent: {
    flex: 1,
  },
  toolContainer: {
    padding: 16,
  },
  toolTitle: {
    fontSize: 16,
    fontWeight: '600',
    fontFamily: 'Rubik',
    marginBottom: 16,
  },
  // Crop Tool Styles
  aspectRatioContainer: {
    marginBottom: 16,
  },
  aspectRatioButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 12,
  },
  aspectRatioText: {
    fontSize: 14,
    fontWeight: '500',
    fontFamily: 'Rubik',
  },
  cropControls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  cropButton: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    marginHorizontal: 4,
  },
  cropButtonText: {
    fontSize: 14,
    fontWeight: '500',
    fontFamily: 'Rubik',
    marginLeft: 8,
  },
  // Text Tool Styles
  textInputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    fontSize: 16,
    fontFamily: 'Rubik',
    marginRight: 12,
    minHeight: 40,
    maxHeight: 80,
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sliderContainer: {
    marginBottom: 16,
  },
  sliderLabel: {
    fontSize: 14,
    fontWeight: '500',
    fontFamily: 'Rubik',
    marginBottom: 8,
  },
  sliderRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sliderTrack: {
    flex: 1,
    height: 4,
    borderRadius: 2,
    marginHorizontal: 12,
    position: 'relative',
  },
  sliderThumb: {
    position: 'absolute',
    width: 20,
    height: 20,
    borderRadius: 10,
    top: -8,
    marginLeft: -10,
  },
  fontContainer: {
    marginBottom: 16,
  },
  fontButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 12,
  },
  fontText: {
    fontSize: 14,
    fontWeight: '500',
  },
  colorContainer: {
    marginBottom: 16,
  },
  colorButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedColor: {
    borderColor: '#FFFFFF',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  // Transform Tool Styles
  rotationControls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  rotateButton: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    marginHorizontal: 4,
  },
  rotateButtonText: {
    fontSize: 14,
    fontWeight: '500',
    fontFamily: 'Rubik',
    marginLeft: 8,
  },
  flipControls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  flipButton: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    marginHorizontal: 4,
  },
  flipButtonText: {
    fontSize: 14,
    fontWeight: '500',
    fontFamily: 'Rubik',
    marginLeft: 8,
  },
  applyButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    marginTop: 8,
  },
  applyButtonText: {
    fontSize: 16,
    fontWeight: '600',
    fontFamily: 'Rubik',
    marginLeft: 8,
  },
});

export default AdvancedEditingTools;
