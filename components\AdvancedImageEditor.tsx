import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Image,
  StyleSheet,
  Dimensions,
  ActivityIndicator,
  Slider,
  TextInput,
  Alert,
  PanGestureHandler,
  PinchGestureHandler,
  State,
} from 'react-native';
import * as ImageManipulator from 'expo-image-manipulator';
import { Feather } from '@expo/vector-icons';
import { useTheme } from '@/src/context/ThemeContext';
import Animated, {
  useAnimatedGestureHandler,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  runOnJS,
} from 'react-native-reanimated';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

interface AdvancedImageEditorProps {
  imageUri: string;
  onSave: (editedUri: string) => void;
  onClose: () => void;
}

interface FilterEffect {
  brightness?: number;
  contrast?: number;
  saturation?: number;
  sepia?: number;
  blur?: number;
  temperature?: number;
}

interface TextOverlay {
  id: string;
  text: string;
  x: number;
  y: number;
  fontSize: number;
  color: string;
  fontFamily: string;
}

const AdvancedImageEditor: React.FC<AdvancedImageEditorProps> = ({
  imageUri,
  onSave,
  onClose,
}) => {
  const { colors, isDarkMode } = useTheme();
  
  // Editor state
  const [currentTab, setCurrentTab] = useState<'filters' | 'adjust' | 'crop' | 'text'>('filters');
  const [isProcessing, setIsProcessing] = useState(false);
  const [editedImageUri, setEditedImageUri] = useState(imageUri);
  
  // Filter effects state
  const [brightness, setBrightness] = useState(1);
  const [contrast, setContrast] = useState(1);
  const [saturation, setSaturation] = useState(1);
  const [sepia, setSepia] = useState(0);
  const [blur, setBlur] = useState(0);
  
  // Text overlay state
  const [textOverlays, setTextOverlays] = useState<TextOverlay[]>([]);
  const [selectedTextId, setSelectedTextId] = useState<string | null>(null);
  const [newText, setNewText] = useState('');
  const [textColor, setTextColor] = useState('#FFFFFF');
  const [fontSize, setFontSize] = useState(24);
  
  // Crop state
  const [cropMode, setCropMode] = useState(false);
  const [cropAspectRatio, setCropAspectRatio] = useState<'free' | '1:1' | '4:3' | '16:9'>('free');
  
  // Visual effect filters
  const visualFilters = [
    { name: 'Original', effect: null },
    { name: 'Sepia', effect: { sepia: 0.8, brightness: 1.1, contrast: 1.1 } },
    { name: 'Blur', effect: { blur: 2 } },
    { name: 'Bright', effect: { brightness: 1.3 } },
    { name: 'High Contrast', effect: { contrast: 1.4 } },
    { name: 'Vibrant', effect: { saturation: 1.5 } },
    { name: 'Cool Tone', effect: { temperature: -0.2, saturation: 1.1 } },
    { name: 'Warm Tone', effect: { temperature: 0.2, brightness: 1.1 } },
    { name: 'Vintage', effect: { sepia: 0.4, contrast: 0.9, brightness: 1.05, saturation: 0.8 } },
    { name: 'Dramatic', effect: { contrast: 1.6, brightness: 0.9, saturation: 1.2 } },
  ];

  // Apply visual effects to image
  const applyVisualEffects = async (sourceUri: string, effects: FilterEffect) => {
    try {
      setIsProcessing(true);
      
      // For now, we'll use ImageManipulator's basic filters
      // In a production app, you might want to use a more advanced image processing library
      const actions: ImageManipulator.Action[] = [];
      
      // Apply blur if specified
      if (effects.blur && effects.blur > 0) {
        // Note: ImageManipulator doesn't have blur, so we'll simulate with resize down/up
        actions.push({ resize: { width: 100 } });
        actions.push({ resize: { width: 800 } });
      }
      
      const result = await ImageManipulator.manipulateAsync(
        sourceUri,
        actions,
        {
          compress: 0.8,
          format: ImageManipulator.SaveFormat.JPEG,
        }
      );
      
      return result.uri;
    } catch (error) {
      console.error('Error applying visual effects:', error);
      return sourceUri;
    } finally {
      setIsProcessing(false);
    }
  };

  // Apply filter preset
  const applyFilterPreset = async (effect: FilterEffect | null) => {
    if (!effect) {
      setEditedImageUri(imageUri);
      return;
    }
    
    const newUri = await applyVisualEffects(imageUri, effect);
    setEditedImageUri(newUri);
    
    // Update individual sliders
    setBrightness(effect.brightness || 1);
    setContrast(effect.contrast || 1);
    setSaturation(effect.saturation || 1);
    setSepia(effect.sepia || 0);
    setBlur(effect.blur || 0);
  };

  // Apply manual adjustments
  const applyManualAdjustments = async () => {
    const effects: FilterEffect = {
      brightness,
      contrast,
      saturation,
      sepia,
      blur,
    };
    
    const newUri = await applyVisualEffects(imageUri, effects);
    setEditedImageUri(newUri);
  };

  // Add text overlay
  const addTextOverlay = () => {
    if (!newText.trim()) return;
    
    const overlay: TextOverlay = {
      id: Date.now().toString(),
      text: newText,
      x: SCREEN_WIDTH / 2,
      y: SCREEN_HEIGHT / 2,
      fontSize,
      color: textColor,
      fontFamily: 'Rubik',
    };
    
    setTextOverlays([...textOverlays, overlay]);
    setNewText('');
  };

  // Remove text overlay
  const removeTextOverlay = (id: string) => {
    setTextOverlays(textOverlays.filter(overlay => overlay.id !== id));
    setSelectedTextId(null);
  };

  // Save edited image
  const handleSave = async () => {
    try {
      setIsProcessing(true);
      
      // Apply final effects and text overlays
      let finalUri = editedImageUri;
      
      // If there are text overlays, we'd need to composite them
      // For now, we'll just return the filtered image
      
      onSave(finalUri);
    } catch (error) {
      console.error('Error saving image:', error);
      Alert.alert('Error', 'Failed to save image. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const renderFiltersTab = () => (
    <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filtersContainer}>
      {visualFilters.map((filter, index) => (
        <TouchableOpacity
          key={index}
          style={[styles.filterButton, { backgroundColor: colors.backgroundSecondary }]}
          onPress={() => applyFilterPreset(filter.effect)}
        >
          <Text style={[styles.filterText, { color: colors.text }]}>{filter.name}</Text>
        </TouchableOpacity>
      ))}
    </ScrollView>
  );

  const renderAdjustTab = () => (
    <ScrollView style={styles.adjustContainer}>
      <View style={styles.sliderContainer}>
        <Text style={[styles.sliderLabel, { color: colors.text }]}>Brightness: {brightness.toFixed(1)}</Text>
        <Slider
          style={styles.slider}
          minimumValue={0.5}
          maximumValue={2}
          value={brightness}
          onValueChange={setBrightness}
          onSlidingComplete={applyManualAdjustments}
          minimumTrackTintColor={colors.primary}
          maximumTrackTintColor={colors.textSecondary}
          thumbStyle={{ backgroundColor: colors.primary }}
        />
      </View>
      
      <View style={styles.sliderContainer}>
        <Text style={[styles.sliderLabel, { color: colors.text }]}>Contrast: {contrast.toFixed(1)}</Text>
        <Slider
          style={styles.slider}
          minimumValue={0.5}
          maximumValue={2}
          value={contrast}
          onValueChange={setContrast}
          onSlidingComplete={applyManualAdjustments}
          minimumTrackTintColor={colors.primary}
          maximumTrackTintColor={colors.textSecondary}
          thumbStyle={{ backgroundColor: colors.primary }}
        />
      </View>
      
      <View style={styles.sliderContainer}>
        <Text style={[styles.sliderLabel, { color: colors.text }]}>Saturation: {saturation.toFixed(1)}</Text>
        <Slider
          style={styles.slider}
          minimumValue={0}
          maximumValue={2}
          value={saturation}
          onValueChange={setSaturation}
          onSlidingComplete={applyManualAdjustments}
          minimumTrackTintColor={colors.primary}
          maximumTrackTintColor={colors.textSecondary}
          thumbStyle={{ backgroundColor: colors.primary }}
        />
      </View>
      
      <View style={styles.sliderContainer}>
        <Text style={[styles.sliderLabel, { color: colors.text }]}>Sepia: {sepia.toFixed(1)}</Text>
        <Slider
          style={styles.slider}
          minimumValue={0}
          maximumValue={1}
          value={sepia}
          onValueChange={setSepia}
          onSlidingComplete={applyManualAdjustments}
          minimumTrackTintColor={colors.primary}
          maximumTrackTintColor={colors.textSecondary}
          thumbStyle={{ backgroundColor: colors.primary }}
        />
      </View>
    </ScrollView>
  );

  const renderTextTab = () => (
    <View style={styles.textContainer}>
      <View style={styles.textInputContainer}>
        <TextInput
          style={[styles.textInput, { color: colors.text, borderColor: colors.border }]}
          placeholder="Enter text..."
          placeholderTextColor={colors.textSecondary}
          value={newText}
          onChangeText={setNewText}
        />
        <TouchableOpacity
          style={[styles.addTextButton, { backgroundColor: colors.primary }]}
          onPress={addTextOverlay}
        >
          <Feather name="plus" size={20} color="#FFFFFF" />
        </TouchableOpacity>
      </View>
      
      <View style={styles.textControls}>
        <View style={styles.sliderContainer}>
          <Text style={[styles.sliderLabel, { color: colors.text }]}>Font Size: {fontSize}</Text>
          <Slider
            style={styles.slider}
            minimumValue={12}
            maximumValue={48}
            value={fontSize}
            onValueChange={setFontSize}
            minimumTrackTintColor={colors.primary}
            maximumTrackTintColor={colors.textSecondary}
            thumbStyle={{ backgroundColor: colors.primary }}
          />
        </View>
      </View>
    </View>
  );

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { borderBottomColor: colors.border }]}>
        <TouchableOpacity onPress={onClose}>
          <Feather name="x" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>Edit Image</Text>
        <TouchableOpacity onPress={handleSave} disabled={isProcessing}>
          {isProcessing ? (
            <ActivityIndicator size="small" color={colors.primary} />
          ) : (
            <Feather name="check" size={24} color={colors.primary} />
          )}
        </TouchableOpacity>
      </View>

      {/* Image Preview */}
      <View style={styles.imageContainer}>
        <Image source={{ uri: editedImageUri }} style={styles.previewImage} resizeMode="contain" />
        
        {/* Text Overlays */}
        {textOverlays.map((overlay) => (
          <TouchableOpacity
            key={overlay.id}
            style={[
              styles.textOverlay,
              {
                left: overlay.x - 50,
                top: overlay.y - 20,
              },
            ]}
            onPress={() => setSelectedTextId(overlay.id)}
            onLongPress={() => removeTextOverlay(overlay.id)}
          >
            <Text
              style={[
                styles.overlayText,
                {
                  fontSize: overlay.fontSize,
                  color: overlay.color,
                  fontFamily: overlay.fontFamily,
                },
              ]}
            >
              {overlay.text}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Tab Navigation */}
      <View style={[styles.tabContainer, { borderTopColor: colors.border }]}>
        {['filters', 'adjust', 'text'].map((tab) => (
          <TouchableOpacity
            key={tab}
            style={[
              styles.tab,
              currentTab === tab && { borderBottomColor: colors.primary },
            ]}
            onPress={() => setCurrentTab(tab as any)}
          >
            <Text
              style={[
                styles.tabText,
                { color: currentTab === tab ? colors.primary : colors.textSecondary },
              ]}
            >
              {tab.charAt(0).toUpperCase() + tab.slice(1)}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Tab Content */}
      <View style={styles.tabContent}>
        {currentTab === 'filters' && renderFiltersTab()}
        {currentTab === 'adjust' && renderAdjustTab()}
        {currentTab === 'text' && renderTextTab()}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    fontFamily: 'Rubik',
  },
  imageContainer: {
    flex: 1,
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  previewImage: {
    width: SCREEN_WIDTH - 32,
    height: (SCREEN_HEIGHT - 300) * 0.8,
    borderRadius: 8,
  },
  textOverlay: {
    position: 'absolute',
    padding: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },
  overlayText: {
    fontFamily: 'Rubik',
    textAlign: 'center',
  },
  tabContainer: {
    flexDirection: 'row',
    borderTopWidth: 1,
    backgroundColor: 'rgba(128, 128, 128, 0.05)',
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  tabText: {
    fontSize: 14,
    fontWeight: '500',
    fontFamily: 'Rubik',
  },
  tabContent: {
    height: 200,
    backgroundColor: 'rgba(128, 128, 128, 0.05)',
  },
  filtersContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 12,
    borderRadius: 20,
    backgroundColor: 'rgba(128, 128, 128, 0.1)',
  },
  filterText: {
    fontSize: 12,
    fontWeight: '500',
    fontFamily: 'Rubik',
  },
  adjustContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  sliderContainer: {
    marginBottom: 16,
  },
  sliderLabel: {
    fontSize: 14,
    fontWeight: '500',
    fontFamily: 'Rubik',
    marginBottom: 8,
  },
  slider: {
    width: '100%',
    height: 40,
  },
  textContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  textInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    fontSize: 16,
    fontFamily: 'Rubik',
    marginRight: 12,
  },
  addTextButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  textControls: {
    marginTop: 8,
  },
});

export default AdvancedImageEditor;
