import React, { useRef, useEffect, useCallback, useMemo, useState } from 'react';
import {
  FlatList,
  View,
  StyleSheet,
  RefreshControl,
  ActivityIndicator,
  Text,
} from 'react-native';
import { useTheme } from '@/src/context/ThemeContext';
import MessageItem from './MessageItem';
import MessageGroup from './MessageGroup';
import DateSeparator from './DateSeparator';
import TypingIndicator from './TypingIndicator';
import { createMessageListItems, MessageListItem } from './utils/messageGrouping';

interface Message {
  id: string;
  content: string;
  sender_id: string;
  receiver_id: string;
  created_at: string;
  status?: 'sent' | 'delivered' | 'read';
  message_type?: 'text' | 'image' | 'shared_post' | 'shared_reel';
  reply_to_message_id?: string;
  reply_to_message?: {
    id: string;
    content: string;
    sender_id: string;
    message_type?: string;
  };
  image_url?: string;
  post_id?: string;
  reel_id?: string;
}

// MessageListItem is now imported from utils

interface MessageListProps {
  messages: Message[];
  currentUserId: string;
  onReply: (message: Message) => void;
  onReaction: (messageId: string, emoji: string) => void;
  onLoadMore: () => void;
  onRefresh: () => void;
  isLoading: boolean;
  isRefreshing: boolean;
  hasMore: boolean;
  isTyping?: boolean;
  typingUsername?: string;
  reactions?: Record<string, Array<{ emoji: string; count: number; userReacted: boolean }>>;
  onMediaPress?: (message: Message) => void;
  isInitialLoading?: boolean; // Instagram-like loading state
}

const MessageList: React.FC<MessageListProps> = ({
  messages,
  currentUserId,
  onReply,
  onReaction,
  onLoadMore,
  onRefresh,
  isLoading,
  isRefreshing,
  hasMore,
  isTyping = false,
  typingUsername,
  reactions = {},
  onMediaPress,
  isInitialLoading = false,
}) => {
  const { colors } = useTheme();
  const flatListRef = useRef<FlatList>(null);

  // Create list items with message grouping and date separators
  const listItems = useMemo(() => {
    const items = createMessageListItems(messages, isTyping, typingUsername);
    // Reverse the order for inverted FlatList (Instagram behavior)
    return items.reverse();
  }, [messages, isTyping, typingUsername]);

  // Instagram-like behavior: Start from bottom on initial load, auto-scroll only for new messages
  const [hasInitiallyScrolled, setHasInitiallyScrolled] = useState(false);
  const [isUserScrolling, setIsUserScrolling] = useState(false);
  const previousMessageCount = useRef(messages.length);

  // With inverted FlatList, it naturally starts from bottom (Instagram behavior)
  useEffect(() => {
    if (listItems.length > 0 && !hasInitiallyScrolled) {
      // Just mark as scrolled since inverted FlatList starts from bottom
      setHasInitiallyScrolled(true);
    }
  }, [listItems.length, hasInitiallyScrolled]);

  // Auto-scroll for new messages with inverted FlatList (scroll to top of inverted list = bottom of chat)
  useEffect(() => {
    if (hasInitiallyScrolled && messages.length > previousMessageCount.current && !isUserScrolling) {
      // For inverted FlatList, scroll to offset 0 (which is the bottom of the chat)
      setTimeout(() => {
        flatListRef.current?.scrollToOffset({ offset: 0, animated: true });
      }, 100);
    }
    previousMessageCount.current = messages.length;
  }, [messages.length, hasInitiallyScrolled, isUserScrolling]);

  const renderItem = useCallback(({ item }: { item: MessageListItem }) => {
    switch (item.type) {
      case 'date':
        return <DateSeparator date={item.date!} />;

      case 'typing':
        return (
          <TypingIndicator
            isVisible={item.isTyping!}
            username={item.username}
          />
        );

      case 'messageGroup':
        const messageGroup = item.data!;
        return (
          <MessageGroup
            messages={messageGroup.messages}
            currentUserId={currentUserId}
            onReply={onReply}
            onReaction={onReaction}
            onMediaPress={onMediaPress}
          />
        );

      default:
        return null;
    }
  }, [currentUserId, onReply, onReaction, reactions, onMediaPress]);

  const keyExtractor = useCallback((item: MessageListItem) => item.id, []);

  const getItemLayout = useCallback((data: any, index: number) => ({
    length: 80, // Estimated item height
    offset: 80 * index,
    index,
  }), []);

  // Handle scroll events to detect user scrolling
  const handleScrollBeginDrag = useCallback(() => {
    setIsUserScrolling(true);
  }, []);

  const handleScrollEndDrag = useCallback(() => {
    // Reset user scrolling flag after a delay
    setTimeout(() => {
      setIsUserScrolling(false);
    }, 1000);
  }, []);

  // Handle scroll to index failures
  const handleScrollToIndexFailed = useCallback((info: any) => {
    // Fallback to scrollToEnd if scrollToIndex fails
    setTimeout(() => {
      flatListRef.current?.scrollToEnd({ animated: false });
    }, 100);
  }, []);

  const renderFooter = () => {
    if (!isLoading || isRefreshing) return null;
    
    return (
      <View style={styles.loadingFooter}>
        <ActivityIndicator size="small" color={colors.primary} />
        <Text style={[styles.loadingText, { color: colors.textSecondary }]} className="font-rubik-regular">
          Loading more messages...
        </Text>
      </View>
    );
  };

  const renderEmpty = () => {
    // Don't show anything in empty state when initial loading overlay is active
    if (isInitialLoading) {
      return null;
    }

    return (
      <View style={styles.emptyContainer}>
        <Text style={[styles.emptyText, { color: colors.textSecondary }]} className="font-rubik-medium">
          No messages yet. Start the conversation!
        </Text>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <FlatList
        ref={flatListRef}
        data={listItems}
        renderItem={renderItem}
        keyExtractor={keyExtractor}
        style={[styles.flatList, { backgroundColor: colors.background }]}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
        inverted={true} // Instagram-like chat behavior - starts from bottom
        onEndReached={hasMore ? onLoadMore : undefined} // With inverted, this loads older messages
        onEndReachedThreshold={0.1}
        onScrollBeginDrag={handleScrollBeginDrag}
        onScrollEndDrag={handleScrollEndDrag}
        onScrollToIndexFailed={handleScrollToIndexFailed}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={onRefresh}
            tintColor={colors.primary}
            colors={[colors.primary]}
          />
        }
        ListFooterComponent={renderFooter}
        ListEmptyComponent={renderEmpty}
        removeClippedSubviews={false} // Disable for better scroll positioning
        maxToRenderPerBatch={20} // Increased for better Instagram-like performance
        windowSize={10} // Increased for better scroll behavior
        initialNumToRender={30} // Show more messages initially
        // Removed getItemLayout to fix scroll positioning issues
        maintainVisibleContentPosition={{
          minIndexForVisible: 0,
          autoscrollToTopThreshold: 100,
        }}
        // Instagram-like optimizations
        keyboardShouldPersistTaps="handled"
        keyboardDismissMode="interactive"
      />

      {/* Initial Loading Overlay - Only for first-time loads */}
      {isInitialLoading && (
        <View style={[styles.loadingOverlay, { backgroundColor: colors.background }]}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.textSecondary }]} className="font-rubik-regular">
            Loading messages...
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
  },
  flatList: {
    flex: 1,
  },
  contentContainer: {
    paddingTop: 16,
    paddingBottom: 8,
  },
  loadingFooter: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
    paddingTop: 100,
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
  },
  loadingText: {
    fontSize: 14,
    textAlign: 'center',
    marginTop: 12,
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
});

export default MessageList;
