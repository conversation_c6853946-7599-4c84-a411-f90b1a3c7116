import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Image,
  StyleSheet,
  Dimensions,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { Feather } from '@expo/vector-icons';
import { useTheme } from '@/src/context/ThemeContext';
import VisualEffectsProcessor, { 
  VISUAL_FILTER_PRESETS, 
  FilterPreset, 
  VisualEffect 
} from './VisualEffectsProcessor';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

interface EnhancedImageFiltersProps {
  imageUri: string;
  onFilterApplied: (filteredUri: string, filterName: string) => void;
  onClose: () => void;
  selectedFilter?: string;
}

const EnhancedImageFilters: React.FC<EnhancedImageFiltersProps> = ({
  imageUri,
  onFilterApplied,
  onClose,
  selectedFilter = 'Original',
}) => {
  const { colors, isDarkMode } = useTheme();
  const [currentFilter, setCurrentFilter] = useState<FilterPreset>(
    VISUAL_FILTER_PRESETS.find(f => f.name === selectedFilter) || VISUAL_FILTER_PRESETS[0]
  );
  const [filteredPreviews, setFilteredPreviews] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);
  const [applyingFilter, setApplyingFilter] = useState(false);
  const [previewProgress, setPreviewProgress] = useState({ completed: 0, total: 0 });

  const effectsProcessor = VisualEffectsProcessor.getInstance();

  // Generate filter previews on mount
  useEffect(() => {
    generatePreviews();
    return () => {
      // Clean up cache when component unmounts
      effectsProcessor.clearCache();
    };
  }, [imageUri]);

  const generatePreviews = useCallback(async () => {
    console.log('Starting enhanced filter preview generation for:', imageUri);
    setLoading(true);
    setPreviewProgress({ completed: 0, total: VISUAL_FILTER_PRESETS.length });

    try {
      const previews = await effectsProcessor.generateBatchPreviews(
        imageUri,
        VISUAL_FILTER_PRESETS,
        120, // Preview size
        (completed, total) => {
          setPreviewProgress({ completed, total });
        }
      );

      setFilteredPreviews(previews);
      console.log(`Generated ${Object.keys(previews).length} filter previews`);
    } catch (error) {
      console.error('Error generating filter previews:', error);
      Alert.alert('Preview Error', 'Failed to generate filter previews. Using original image.');
      
      // Fallback to original image for all previews
      const fallbackPreviews: Record<string, string> = {};
      VISUAL_FILTER_PRESETS.forEach(filter => {
        fallbackPreviews[filter.name] = imageUri;
      });
      setFilteredPreviews(fallbackPreviews);
    } finally {
      setLoading(false);
      setPreviewProgress({ completed: 0, total: 0 });
    }
  }, [imageUri, effectsProcessor]);

  const applyFilter = async (filter: FilterPreset) => {
    if (filter.name === currentFilter.name) {
      // If same filter is selected, apply it and close
      handleApplyAndClose();
      return;
    }

    setCurrentFilter(filter);

    if (filter.name === 'Original') {
      // No processing needed for original
      return;
    }

    try {
      setApplyingFilter(true);
      
      // Apply the filter to the full-size image
      const filteredUri = await effectsProcessor.applyVisualEffect(
        imageUri,
        filter.effect!,
        {
          maintainDimensions: true,
          quality: 0.9,
        }
      );

      console.log(`Applied filter ${filter.name} to full-size image`);
    } catch (error) {
      console.error('Error applying filter:', error);
      Alert.alert('Filter Error', 'Failed to apply filter. Please try again.');
    } finally {
      setApplyingFilter(false);
    }
  };

  const handleApplyAndClose = async () => {
    try {
      setApplyingFilter(true);

      let finalUri = imageUri;
      
      if (currentFilter.name !== 'Original' && currentFilter.effect) {
        finalUri = await effectsProcessor.applyVisualEffect(
          imageUri,
          currentFilter.effect,
          {
            maintainDimensions: true,
            quality: 0.9,
          }
        );
      }

      onFilterApplied(finalUri, currentFilter.name);
    } catch (error) {
      console.error('Error applying final filter:', error);
      Alert.alert('Error', 'Failed to apply filter. Using original image.');
      onFilterApplied(imageUri, 'Original');
    } finally {
      setApplyingFilter(false);
    }
  };

  const renderFilterPreview = (filter: FilterPreset, index: number) => {
    const isSelected = currentFilter.name === filter.name;
    const previewUri = filteredPreviews[filter.name] || imageUri;
    const isLoading = loading && !filteredPreviews[filter.name];

    return (
      <TouchableOpacity
        key={index}
        style={[
          styles.filterContainer,
          isSelected && { borderColor: colors.primary, borderWidth: 2 },
        ]}
        onPress={() => applyFilter(filter)}
        disabled={applyingFilter}
      >
        <View style={styles.filterImageContainer}>
          {isLoading ? (
            <View style={[styles.filterImage, styles.loadingContainer]}>
              <ActivityIndicator size="small" color={colors.primary} />
            </View>
          ) : (
            <Image
              source={{ uri: previewUri }}
              style={styles.filterImage}
              resizeMode="cover"
            />
          )}
          
          {/* Filter overlay effect for visual representation */}
          {filter.effect && (
            <View style={[styles.filterOverlay, getFilterOverlayStyle(filter.effect)]} />
          )}
        </View>
        
        <Text style={[styles.filterName, { color: colors.text }]} numberOfLines={1}>
          {filter.name}
        </Text>
        
        {filter.description && (
          <Text style={[styles.filterDescription, { color: colors.textSecondary }]} numberOfLines={2}>
            {filter.description}
          </Text>
        )}
      </TouchableOpacity>
    );
  };

  const getFilterOverlayStyle = (effect: VisualEffect) => {
    const overlayStyle: any = {};
    
    if (effect.sepia && effect.sepia > 0) {
      overlayStyle.backgroundColor = `rgba(139, 69, 19, ${effect.sepia * 0.3})`;
    } else if (effect.temperature) {
      if (effect.temperature > 0) {
        overlayStyle.backgroundColor = `rgba(255, 107, 53, ${Math.abs(effect.temperature) * 0.5})`;
      } else {
        overlayStyle.backgroundColor = `rgba(74, 144, 226, ${Math.abs(effect.temperature) * 0.5})`;
      }
    } else if (effect.contrast && effect.contrast > 1.3) {
      overlayStyle.backgroundColor = `rgba(255, 255, 255, 0.1)`;
    }
    
    return overlayStyle;
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { borderBottomColor: colors.border }]}>
        <TouchableOpacity onPress={onClose} disabled={applyingFilter}>
          <Feather name="x" size={24} color={colors.text} />
        </TouchableOpacity>
        
        <View style={styles.headerCenter}>
          <Text style={[styles.headerTitle, { color: colors.text }]}>Filters</Text>
          {loading && (
            <Text style={[styles.progressText, { color: colors.textSecondary }]}>
              {previewProgress.completed}/{previewProgress.total}
            </Text>
          )}
        </View>
        
        <TouchableOpacity 
          onPress={handleApplyAndClose} 
          disabled={applyingFilter || loading}
          style={[styles.applyButton, { opacity: (applyingFilter || loading) ? 0.5 : 1 }]}
        >
          {applyingFilter ? (
            <ActivityIndicator size="small" color={colors.primary} />
          ) : (
            <Feather name="check" size={24} color={colors.primary} />
          )}
        </TouchableOpacity>
      </View>

      {/* Current Filter Info */}
      <View style={[styles.currentFilterInfo, { backgroundColor: colors.backgroundSecondary }]}>
        <Text style={[styles.currentFilterName, { color: colors.text }]}>
          {currentFilter.name}
        </Text>
        {currentFilter.description && (
          <Text style={[styles.currentFilterDescription, { color: colors.textSecondary }]}>
            {currentFilter.description}
          </Text>
        )}
      </View>

      {/* Filter Categories */}
      <View style={styles.categoriesContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <TouchableOpacity style={[styles.categoryButton, { backgroundColor: colors.primary }]}>
            <Text style={[styles.categoryText, { color: '#FFFFFF' }]}>All</Text>
          </TouchableOpacity>
          <TouchableOpacity style={[styles.categoryButton, { backgroundColor: colors.backgroundSecondary }]}>
            <Text style={[styles.categoryText, { color: colors.text }]}>Vintage</Text>
          </TouchableOpacity>
          <TouchableOpacity style={[styles.categoryButton, { backgroundColor: colors.backgroundSecondary }]}>
            <Text style={[styles.categoryText, { color: colors.text }]}>Modern</Text>
          </TouchableOpacity>
          <TouchableOpacity style={[styles.categoryButton, { backgroundColor: colors.backgroundSecondary }]}>
            <Text style={[styles.categoryText, { color: colors.text }]}>Artistic</Text>
          </TouchableOpacity>
        </ScrollView>
      </View>

      {/* Filters Grid */}
      <ScrollView 
        style={styles.filtersScrollView}
        contentContainerStyle={styles.filtersContainer}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.filtersGrid}>
          {VISUAL_FILTER_PRESETS.map((filter, index) => renderFilterPreview(filter, index))}
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  headerCenter: {
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    fontFamily: 'Rubik',
  },
  progressText: {
    fontSize: 12,
    fontFamily: 'Rubik',
    marginTop: 2,
  },
  applyButton: {
    padding: 4,
  },
  currentFilterInfo: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(128, 128, 128, 0.2)',
  },
  currentFilterName: {
    fontSize: 16,
    fontWeight: '600',
    fontFamily: 'Rubik',
  },
  currentFilterDescription: {
    fontSize: 14,
    fontFamily: 'Rubik',
    marginTop: 4,
  },
  categoriesContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(128, 128, 128, 0.2)',
  },
  categoryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 12,
  },
  categoryText: {
    fontSize: 14,
    fontWeight: '500',
    fontFamily: 'Rubik',
  },
  filtersScrollView: {
    flex: 1,
  },
  filtersContainer: {
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  filtersGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  filterContainer: {
    width: (SCREEN_WIDTH - 48) / 3,
    marginBottom: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'transparent',
    overflow: 'hidden',
  },
  filterImageContainer: {
    position: 'relative',
    width: '100%',
    aspectRatio: 1,
  },
  filterImage: {
    width: '100%',
    height: '100%',
    borderRadius: 6,
  },
  filterOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    borderRadius: 6,
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(128, 128, 128, 0.1)',
  },
  filterName: {
    fontSize: 12,
    fontWeight: '500',
    fontFamily: 'Rubik',
    textAlign: 'center',
    marginTop: 8,
    marginHorizontal: 4,
  },
  filterDescription: {
    fontSize: 10,
    fontFamily: 'Rubik',
    textAlign: 'center',
    marginTop: 2,
    marginHorizontal: 4,
  },
});

export default EnhancedImageFilters;
